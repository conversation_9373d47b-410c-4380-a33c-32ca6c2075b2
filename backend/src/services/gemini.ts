// @ts-nocheck
/**
 * Gemini AI 服务
 * 处理文本生成、图像生成和语音合成
 */

import { GoogleGenAI } from '@google/genai';
import { CreateStoryRequest, StoryPage, StoryStyle, VoiceType } from '../types/api';

export interface GeminiStoryResponse {
  title: string;
  pages: Array<{
    pageNumber: number;
    text: string;
    imagePrompt: string;
  }>;
  fullText: string;
}

export class GeminiService {
  private ai: GoogleGenAI;

  constructor(apiKey: string) {
    this.ai = new GoogleGenAI({
      apiKey: apiKey
    });
  }

  /**
   * 生成故事文本
   */
  async generateStory(request: CreateStoryRequest, subscription?: any): Promise<GeminiStoryResponse> {
    const prompt = this.buildStoryPrompt(request);
    
    // 导入订阅服务（在函数内导入避免循环依赖）
    const { SubscriptionService } = await import('./subscription');
    
    // 获取用户订阅对应的AI模型配置
    const aiConfig = SubscriptionService.getAIModelConfig(subscription);
    
    console.log(`🤖 使用${subscription?.plan || 'free'}级别AI模型: ${aiConfig.model}`);
    console.log(`   - 温度: ${aiConfig.temperature}, topK: ${aiConfig.topK}, 最大输出: ${aiConfig.maxOutputTokens}`);

    try {
      console.log('Generating story with @google/genai package...');
      console.log('Using text prompt with JSON schema specification...');

      const response = await this.ai.models.generateContent({
        model: aiConfig.model,
        contents: prompt,
        config: {
          temperature: aiConfig.temperature,
          topK: aiConfig.topK,
          topP: 0.95,
          maxOutputTokens: 8192,
          thinkingConfig: {
            thinkingBudget: 0, // Disable thinking to save tokens for actual content
          },
          safetySettings: [
            {
              category: 'HARM_CATEGORY_HARASSMENT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_HATE_SPEECH',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            }
          ]
        }
      });

      console.log('Story generation response received');
      console.log('Response structure:', JSON.stringify(response, null, 2));

      // @google/genai的响应结构可能是 response.candidates[0].content.parts[0].text
      let text: string;
      if (response.text) {
        text = response.text;
      } else if (response.candidates && response.candidates[0] && response.candidates[0].content && response.candidates[0].content.parts && response.candidates[0].content.parts[0]) {
        text = response.candidates[0].content.parts[0].text;
      } else {
        console.error('No text in response:', response);
        throw new Error('No text content in response');
      }
      console.log('Generated text length:', text.length);

      return this.parseStoryResponse(text);
    } catch (error) {
      console.error('Story generation failed:', error);
      throw new Error(`故事生成失败: ${error.message}`);
    }
  }

  /**
   * 生成单张图像 - 用于角色形象生成
   */
  async generateSingleImage(prompt: string, style: StoryStyle, subscription?: any): Promise<string> {
    console.log(`🎨 [GeminiService] 生成单张图像，风格: ${style}`);

    try {
      // 设置超时控制
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('图像生成超时 (45秒)')), 45000);
      });

      // 优先使用 Imagen 4.0 生成高质量图像
      const generatePromise = this.ai.models.generateImages({
        model: 'imagen-4.0-generate-preview-06-06',
        prompt: prompt,
        config: {
          numberOfImages: 1,
        },
      });

      const response = await Promise.race([generatePromise, timeoutPromise]);

      if (response.generatedImages && response.generatedImages.length > 0) {
        const imageBytes = response.generatedImages[0].image.imageBytes;

        if (!imageBytes || imageBytes.length === 0) {
          throw new Error('图像数据为空');
        }

        const imageDataUrl = `data:image/png;base64,${imageBytes}`;
        console.log(`✅ [GeminiService] 使用 Imagen 4.0 生成成功，数据大小: ${imageBytes.length} 字符`);
        return imageDataUrl;
      } else {
        throw new Error('Imagen API返回的响应中没有图片数据');
      }

    } catch (error) {
      console.warn(`⚠️ [GeminiService] Imagen 4.0 生成失败，尝试 Gemini 2.0 备用方案: ${this.formatError(error)}`);

      // 备用方案：使用 Gemini 2.0
      try {
        const response = await this.ai.models.generateImages({
          model: 'gemini-2.0-flash-exp',
          prompt: prompt,
          config: {
            numberOfImages: 1,
          },
        });

        if (response.generatedImages && response.generatedImages.length > 0) {
          const imageBytes = response.generatedImages[0].image.imageBytes;

          if (!imageBytes || imageBytes.length === 0) {
            throw new Error('Gemini 2.0 图像数据为空');
          }

          const imageDataUrl = `data:image/png;base64,${imageBytes}`;
          console.log(`✅ [GeminiService] 使用 Gemini 2.0 备用方案生成成功`);
          return imageDataUrl;
        } else {
          throw new Error('Gemini 2.0 API返回的响应中没有图片数据');
        }
      } catch (geminiError) {
        console.error(`❌ [GeminiService] Gemini 2.0 备用方案也失败: ${this.formatError(geminiError)}`);
        throw new Error('所有图像生成方案都失败了');
      }
    }
  }

  /**
   * 生成图像 - 视觉锚定工作流
   */
  async generateImages(imagePrompts: string[], style: StoryStyle, subscription?: any): Promise<string[]> {
    console.log(`🎨 [GeminiService] 启动视觉锚定工作流，风格: ${style}`);

    // 导入订阅服务
    const { SubscriptionService } = await import('./subscription');

    // 获取用户订阅对应的图片生成配置
    const imageConfig = SubscriptionService.getImageGenerationConfig(subscription);

    console.log(`🖼️ 使用${subscription?.plan || 'free'}级别图片生成: ${imageConfig.model}`);
    console.log(`   - 分辨率: ${imageConfig.resolution}, 质量: ${imageConfig.quality}`);

    // 使用新的视觉锚定工作流
    return await this.generateImagesWithVisualAnchor(imagePrompts, style, subscription);
  }

  /**
   * 视觉锚定工作流 - 三阶段生成
   */
  async generateImagesWithVisualAnchor(
    editPrompts: string[],
    style: StoryStyle,
    subscription?: any,
    characterImageUrl?: string
  ): Promise<string[]> {
    console.log(`🔗 [视觉锚定工作流] 开始三阶段生成流程`);

    try {
      // 阶段一: 获取角色基准图 (Visual Anchor)
      console.log(`📍 [阶段1/3] 获取角色基准图...`);

      // 使用预生成的角色形象（如果有）
      let characterAnchor: string;
      if (characterImageUrl) {
        console.log(`✅ [阶段1/3] 使用预生成的角色形象: ${characterImageUrl}`);
        characterAnchor = characterImageUrl;
      } else {
        console.log(`📍 [阶段1/3] 没有预生成的角色形象，动态生成...`);
        characterAnchor = await this.generateCharacterAnchor(style, subscription);
        console.log(`✅ [阶段1/3] 角色基准图生成完成`);
      }

      // 阶段二: 基于基准图逐页生成插图
      console.log(`🎨 [阶段2/3] 基于基准图生成 ${editPrompts.length} 张插图...`);
      const images: string[] = [];
      let successCount = 0;
      let failureCount = 0;

      for (let i = 0; i < editPrompts.length; i++) {
        const editPrompt = editPrompts[i];
        console.log(`🖼️ [视觉锚定] 生成第 ${i + 1}/${editPrompts.length} 张插图`);
        console.log(`   编辑指令: ${editPrompt.substring(0, 100)}${editPrompt.length > 100 ? '...' : ''}`);

        try {
          const imageResult = await this.generateImageFromAnchor(characterAnchor, editPrompt, style, i + 1, subscription);
          images.push(imageResult);
          successCount++;
          console.log(`✅ [视觉锚定] 第 ${i + 1} 张插图生成成功`);
        } catch (error) {
          failureCount++;
          console.error(`❌ [视觉锚定] 第 ${i + 1} 张插图生成失败:`, this.formatError(error));

          // 使用基准图生成占位符
          const placeholderImage = await this.generatePlaceholderFromAnchor(characterAnchor, `第${i + 1}页`, style);
          images.push(placeholderImage);
          console.log(`🔄 [视觉锚定] 使用基准图占位符替代第 ${i + 1} 张插图`);
        }

        // 添加延迟避免请求过快
        if (i < editPrompts.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1500));
        }
      }

      console.log(`🎯 [视觉锚定工作流] 完成 - 成功: ${successCount}, 失败: ${failureCount}, 总计: ${images.length}`);
      return images;

    } catch (error) {
      console.error(`❌ [视觉锚定工作流] 关键错误，降级到传统模式:`, this.formatError(error));
      // 降级到传统图片生成模式
      return await this.generateImagesTraditional(editPrompts, style, subscription);
    }
  }

  /**
   * 生成单张图片（带重试机制）
   */
  private async generateSingleImageWithRetry(
    prompt: string,
    style: StoryStyle,
    styleDescription: string,
    imageNumber: number,
    maxRetries: number = 2,
    subscription?: any
  ): Promise<string> {
    let lastError: Error | null = null;

    // 导入订阅服务
    const { SubscriptionService } = await import('./subscription');

    // 获取用户订阅对应的图片生成配置
    const imageConfig = SubscriptionService.getImageGenerationConfig(subscription);

    // 使用订阅级别对应的重试次数
    const retries = imageConfig.maxRetries || maxRetries;

    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        console.log(`   🔄 [GeminiService] 第 ${imageNumber} 张图片，尝试 ${attempt}/${retries}`);

        // 根据订阅级别决定是否使用增强提示词
        let enhancedPrompt;
        if (imageConfig.enhancedPrompt) {
          enhancedPrompt = `
            ${prompt}

            风格要求：${styleDescription}
            画面要求：适合儿童观看，温馨友好，无暴力内容，高质量插图
            技术要求：${imageConfig.quality === 'ultra' ? '超高清' : imageConfig.quality === 'premium' ? '高清' : '标准清晰度'}，
                    细节${imageConfig.quality === 'standard' ? '清晰' : '丰富'}，
                    色彩和谐，${imageConfig.quality === 'ultra' ? '专业艺术级' : '专业'}插画水准
            分辨率：${imageConfig.resolution}
          `;
        } else {
          enhancedPrompt = `
            ${prompt}
            风格：${styleDescription}
            适合儿童观看，温馨友好
          `;
        }

        // 为了节省测试成本，直接使用 Gemini 2.0 备用方案
        console.log(`   💰 [GeminiService] 使用 Gemini 2.0 备用方案（节省成本模式）`);
        try {
          const geminiResult = await this.generateImageWithGemini(enhancedPrompt, imageNumber);
          return geminiResult;
        } catch (geminiError) {
          console.warn(`   ⚠️ [GeminiService] Gemini 2.0 备用方案失败: ${this.formatError(geminiError)}`);

          // 如果备用方案也失败，尝试 Imagen 4.0 作为最后手段
          console.log(`   🔄 [GeminiService] 尝试 Imagen 4.0 作为最后手段`);
          try {
            const imageResult = await this.generateImageWithImagen(enhancedPrompt, imageNumber);
            return imageResult;
          } catch (imagenError) {
            console.warn(`   ⚠️ [GeminiService] Imagen 4.0 也失败: ${this.formatError(imagenError)}`);
            throw imagenError;
          }
        }

      } catch (error) {
        lastError = error as Error;
        console.warn(`   ⚠️ [GeminiService] 第 ${imageNumber} 张图片第 ${attempt} 次尝试失败: ${this.formatError(error)}`);

        if (attempt < retries) {
          // 等待后重试
          const retryDelay = attempt * 2000; // 递增延迟
          console.log(`   ⏳ [GeminiService] ${retryDelay/1000} 秒后重试...`);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
        }
      }
    }

    throw lastError || new Error(`图片生成失败，已重试 ${retries} 次`);
  }

  /**
   * 使用 Imagen 4.0 模型生成图片
   */
  private async generateImageWithImagen(prompt: string, imageNumber: number): Promise<string> {
    console.log(`   🎨 [GeminiService] 使用 Imagen 4.0 生成第 ${imageNumber} 张图片`);

    // 设置超时控制
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Imagen 图片生成超时 (30秒)')), 30000);
    });

    const generatePromise = this.ai.models.generateImages({
      model: 'imagen-4.0-generate-preview-06-06',
      prompt: prompt,
      config: {
        numberOfImages: 1,
      },
    });

    const response = await Promise.race([generatePromise, timeoutPromise]);

    if (response.generatedImages && response.generatedImages.length > 0) {
      const imageBytes = response.generatedImages[0].image.imageBytes;

      // 验证base64数据
      if (!imageBytes || imageBytes.length === 0) {
        throw new Error('Imagen 生成的图片数据为空');
      }

      const imageDataUrl = `data:image/png;base64,${imageBytes}`;
      console.log(`   ✅ [GeminiService] Imagen 4.0 第 ${imageNumber} 张图片生成成功，数据大小: ${imageBytes.length} 字符`);
      return imageDataUrl;
    } else {
      throw new Error('Imagen API返回的响应中没有图片数据');
    }
  }

  /**
   * 使用 Gemini 2.0 模型生成图片（备用方案）
   */
  private async generateImageWithGemini(prompt: string, imageNumber: number): Promise<string> {
    console.log(`   🔄 [GeminiService] 使用 Gemini 2.0 备用方案生成第 ${imageNumber} 张图片`);

    // 导入 Modality 枚举
    const { Modality } = await import('@google/genai');

    // 明确要求生成图片的提示词
    const imagePrompt = `请生成图片：${prompt}。请务必生成一张图片作为输出。`;

    // 设置超时控制
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Gemini 图片生成超时 (30秒)')), 30000);
    });

    const generatePromise = this.ai.models.generateContent({
      model: 'gemini-2.0-flash-preview-image-generation',
      contents: imagePrompt,
      config: {
        responseModalities: [Modality.TEXT, Modality.IMAGE],
        temperature: 0.7,
        maxOutputTokens: 1024,
      },
    });

    const response = await Promise.race([generatePromise, timeoutPromise]);

    // 检查响应中是否包含图片数据
    if (response.candidates && response.candidates.length > 0) {
      const parts = response.candidates[0].content.parts;

      for (const part of parts) {
        if (part.inlineData && part.inlineData.data) {
          const imageData = part.inlineData.data;
          const imageDataUrl = `data:image/png;base64,${imageData}`;
          console.log(`   ✅ [GeminiService] Gemini 2.0 第 ${imageNumber} 张图片生成成功，数据大小: ${imageData.length} 字符`);
          return imageDataUrl;
        }
      }

      // 如果没有找到图片数据，但有文本输出，记录文本内容
      for (const part of parts) {
        if (part.text) {
          console.log(`   📝 [GeminiService] Gemini 2.0 文本输出: ${part.text.substring(0, 200)}...`);
        }
      }

      throw new Error('Gemini 2.0 模型没有生成图片，只输出了文本');
    } else {
      throw new Error('Gemini 2.0 API返回的响应中没有有效数据');
    }
  }

  /**
   * 格式化错误信息
   */
  private formatError(error: any): string {
    if (error.name === 'AbortError') {
      return '请求超时';
    } else if (error.message?.includes('rate limit')) {
      return 'API请求频率限制';
    } else if (error.message?.includes('quota')) {
      return 'API配额不足';
    } else if (error.message?.includes('network')) {
      return '网络连接错误';
    } else if (error.status) {
      return `API错误 (${error.status}): ${error.message || '未知错误'}`;
    } else {
      return error.message || '未知错误';
    }
  }

  /**
   * 生成语音 - 使用LibreTTS替代Google Gemini TTS
   */
  async generateAudio(text: string, voice: VoiceType, subscription?: any): Promise<string> {
    console.log(`🔊 [TTS] 生成音频: 文本长度=${text.length}, 声音=${voice}`);

    try {
      // 导入LibreTTS服务
      const { LibreTTSService } = await import('./libreTTS');
      const libreTTSService = new LibreTTSService();

      // 使用LibreTTS生成音频
      const audioDataUrl = await libreTTSService.generateAudio(text, voice, subscription);

      console.log(`✅ [TTS] LibreTTS音频生成成功`);
      return audioDataUrl;

    } catch (error) {
      console.error('❌ [TTS] LibreTTS音频生成失败:', error);

      // 回退到占位符音频
      console.log('🔄 [TTS] 使用占位符音频');
      const placeholderAudioBase64 = this.generatePlaceholderAudio(text.substring(0, 50));
      return placeholderAudioBase64;
    }
  }

  /**
   * 构建故事生成提示词 - 视觉锚定工作流版本
   */
  private buildStoryPrompt(request: CreateStoryRequest): string {
    const traits = Array.isArray(request.characterTraits) ? request.characterTraits.join('、') : '善良、勇敢';

    return `
你是一位专业的儿童故事编剧。你将为一个已经设计好的角色创作故事脚本。

**角色与故事背景:**
- **主角**: ${request.characterName}，一个${request.characterAge}岁的孩子
- **性格特征**: ${traits}
- **故事主题**: ${request.theme}
- **故事场景**: ${request.setting}

**任务要求:**
1. 创作一个包含6-8页的完整故事
2. 故事要积极正面，富有教育意义，结局温馨美好
3. 每页文本50-80字，语言简单易懂
4. 包含冒险、友谊、成长等正面元素
5. 为每一页生成一个**editPrompt**（编辑指令）

**关于editPrompt的重要说明:**
editPrompt是一个清晰的指令，告诉图像模型如何基于一个现有的角色图片进行修改，以匹配当页的故事情节。
- **只描述变化的部分**：姿势、表情、环境、场景元素
- **假定角色的基本外观是已知的**：不需要重复描述角色的发型、脸型、服装等基本特征
- **专注于动作和场景**：描述角色在做什么、在哪里、周围有什么

**editPrompt示例:**
- "将角色置于阳光明媚的森林里，他正好奇地蹲下身，触摸一朵发光的蘑菇，脸上带着惊讶和喜悦的表情"
- "现在让角色和一只毛茸茸的小松鼠并肩坐在粗壮的树枝上，他正开心地笑着，与松鼠分享一颗橡子"
- "角色站在山顶上，张开双臂拥抱微风，背景是壮丽的日落和连绵的山脉，表情充满成就感"

**输出格式 (严格JSON格式):**
请严格按照以下JSON格式返回结果：

{
  "title": "故事标题（字符串）",
  "pages": [
    {
      "pageNumber": 1,
      "text": "第一页的故事文字内容（字符串，50-80字）",
      "editPrompt": "基于现有角色图片的编辑指令（字符串）"
    },
    {
      "pageNumber": 2,
      "text": "第二页的故事文字内容（字符串，50-80字）",
      "editPrompt": "基于现有角色图片的编辑指令（字符串）"
    }
    // ... 继续6-8页
  ],
  "fullText": "将所有页面文字连接成的完整故事文本（字符串）"
}

**重要提醒:**
- 请只返回JSON格式的数据，不要包含任何其他文字或标记
- editPrompt应该简洁明确，专注于场景变化和角色动作
- 确保故事情节连贯，每页的editPrompt都能推进故事发展
`;
  }

  /**
   * 解析故事响应
   */
  private parseStoryResponse(text: string): GeminiStoryResponse {
    try {
      console.log('Parsing story response with responseSchema...');
      console.log('Response text length:', text.length);
      console.log('Response preview:', text.substring(0, 200) + '...');

      // 清理可能的markdown代码块标记
      let cleanText = text.trim();
      if (cleanText.startsWith('```json')) {
        cleanText = cleanText.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        console.log('Cleaned markdown code blocks from response');
      }
      if (cleanText.startsWith('```')) {
        cleanText = cleanText.replace(/^```\s*/, '').replace(/\s*```$/, '');
        console.log('Cleaned generic code blocks from response');
      }

      // 使用responseSchema后，响应应该已经是纯JSON格式
      const parsed = JSON.parse(cleanText);

      // 验证响应格式
      if (!parsed.title || !parsed.pages || !Array.isArray(parsed.pages)) {
        throw new Error('Invalid story format: missing required fields');
      }

      // 验证每页的格式 - 支持新的editPrompt字段
      const validPages = [];
      for (const page of parsed.pages) {
        if (page.pageNumber && page.text && (page.editPrompt || page.imagePrompt)) {
          // 兼容性处理：如果有editPrompt就使用editPrompt，否则使用imagePrompt
          if (page.editPrompt && !page.imagePrompt) {
            page.imagePrompt = page.editPrompt; // 为了向后兼容
          }
          validPages.push(page);
        } else {
          console.log('Skipping incomplete page:', page);
        }
      }

      if (validPages.length === 0) {
        throw new Error('No valid pages found');
      }

      parsed.pages = validPages;
      console.log(`✅ Successfully parsed story with ${validPages.length} valid pages`);

      return parsed;
    } catch (error) {
      console.error('Story parsing failed:', error);
      console.error('Text that failed to parse:', text.substring(0, 500) + '...');
      throw new Error('故事格式解析失败，请重试');
    }
  }

  /**
   * 内容安全检查
   */
  async checkContentSafety(text: string): Promise<boolean> {
    try {
      console.log('Performing content safety check...');

      const response = await this.ai.models.generateContent({
        model: "gemini-2.5-flash",
        contents: `请检查以下内容是否适合儿童阅读，如果适合请回复"SAFE"，如果不适合请回复"UNSAFE"并说明原因：\n\n${text}`,
        config: {
          temperature: 0.1,
          maxOutputTokens: 100,
          safetySettings: [
            {
              category: 'HARM_CATEGORY_HARASSMENT',
              threshold: 'BLOCK_LOW_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_HATE_SPEECH',
              threshold: 'BLOCK_LOW_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
              threshold: 'BLOCK_LOW_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
              threshold: 'BLOCK_LOW_AND_ABOVE'
            }
          ]
        }
      });

      if (!response.text) {
        console.warn('Content safety check failed, assuming safe');
        return true; // 如果检查失败，默认认为安全
      }

      const result = response.text;
      console.log('Safety check result:', result);

      return result.includes('SAFE');
    } catch (error) {
      console.error('Content safety check failed:', error);
      return true; // 如果检查失败，默认认为安全
    }
  }

  /**
   * 阶段一: 生成角色基准图 (Visual Anchor)
   */
  private async generateCharacterAnchor(style: StoryStyle, subscription?: any): Promise<string> {
    console.log(`📍 [角色基准图] 开始生成视觉锚点，风格: ${style}`);

    const styleDescriptions = {
      cartoon: "皮克斯动画风格，色彩鲜艳，线条柔和，适合儿童，温馨友好的卡通风格",
      watercolor: "精美水彩画风格，柔和色调，艺术感强，梦幻效果，手绘质感",
      sketch: "简洁简笔画风格，线条清晰，黑白或淡彩，手绘感强",
      fantasy: "奇幻插画风格，梦幻色彩，魔法元素，想象力丰富，童话感",
      realistic: "写实插画风格，细节丰富，自然色彩，真实感强但适合儿童",
      anime: "日式动漫风格，大眼睛，夸张表情，色彩饱和度高"
    };

    const styleDescription = styleDescriptions[style] || styleDescriptions.cartoon;

    const anchorPrompt = `
你是一位服务于顶尖动画工作室的角色设计师。你的任务是为一本儿童绘本创作主角的概念设定图。

**图像生成指令:**
请生成一张高质量的儿童角色全身设定图。

**角色要求:**
- 年龄: 5-8岁的可爱儿童角色
- 性格: 友善、好奇、勇敢
- 服装: 简洁但有特色的日常服装（便于后续场景变化）

**构图要求 (极其重要):**
1. **姿势**: 角色采用中性的站立姿势，正面朝向镜头，双臂自然下垂
2. **背景**: 纯白色背景，无任何场景元素或装饰
3. **风格**: ${styleDescription}
4. **细节**: 角色特征清晰，面部表情友善自然，服装细节完整
5. **构图**: 全身像，角色居中，留有适当边距便于后续编辑

**技术要求:**
- 高分辨率，细节丰富
- 色彩和谐，适合儿童观看
- 这张图片将作为后续所有插图的视觉基准，请确保质量

请只生成一张图片，不要附带任何文字。
    `.trim();

    try {
      // 设置超时控制
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('角色基准图生成超时 (45秒)')), 45000);
      });

      // 优先使用 Imagen 4.0 生成高质量基准图
      const generatePromise = this.ai.models.generateImages({
        model: 'imagen-4.0-generate-preview-06-06',
        prompt: anchorPrompt,
        config: {
          numberOfImages: 1,
        },
      });

      const response = await Promise.race([generatePromise, timeoutPromise]);

      if (response.generatedImages && response.generatedImages.length > 0) {
        const imageBytes = response.generatedImages[0].image.imageBytes;
        
        if (!imageBytes || imageBytes.length === 0) {
          throw new Error('角色基准图数据为空');
        }

        const imageDataUrl = `data:image/png;base64,${imageBytes}`;
        console.log(`✅ [角色基准图] 使用 Imagen 4.0 生成成功，数据大小: ${imageBytes.length} 字符`);
        return imageDataUrl;
      } else {
        throw new Error('Imagen API返回的响应中没有图片数据');
      }

    } catch (error) {
      console.warn(`⚠️ [角色基准图] Imagen 4.0 生成失败，尝试 Gemini 2.0 备用方案: ${this.formatError(error)}`);
      
      try {
        // 备用方案：使用 Gemini 2.0
        const { Modality } = await import('@google/genai');
        
        const geminiResponse = await this.ai.models.generateContent({
          model: 'gemini-2.0-flash-preview-image-generation',
          contents: anchorPrompt,
          config: {
            responseModalities: [Modality.TEXT, Modality.IMAGE],
            temperature: 0.7,
            maxOutputTokens: 1024,
          },
        });

        if (geminiResponse.candidates && geminiResponse.candidates.length > 0) {
          const parts = geminiResponse.candidates[0].content.parts;
          
          for (const part of parts) {
            if (part.inlineData && part.inlineData.data) {
              const imageData = part.inlineData.data;
              const imageDataUrl = `data:image/png;base64,${imageData}`;
              console.log(`✅ [角色基准图] 使用 Gemini 2.0 备用方案生成成功`);
              return imageDataUrl;
            }
          }
        }
        
        throw new Error('Gemini 2.0 备用方案也未能生成角色基准图');
        
      } catch (geminiError) {
        console.error(`❌ [角色基准图] 所有生成方案失败，使用默认基准图`);
        // 生成一个简单的默认角色基准图
        return this.generateDefaultCharacterAnchor(style);
      }
    }
  }

  /**
   * 基于角色基准图生成单张插图
   */
  private async generateImageFromAnchor(
    characterAnchor: string, 
    editPrompt: string, 
    style: StoryStyle, 
    pageNumber: number,
    subscription?: any
  ): Promise<string> {
    console.log(`🎨 [图像编辑] 基于基准图生成第 ${pageNumber} 张插图`);

    const styleDescriptions = {
      cartoon: "皮克斯动画风格，色彩鲜艳，线条柔和",
      watercolor: "精美水彩画风格，柔和色调，艺术感强",
      sketch: "简洁简笔画风格，线条清晰",
      fantasy: "奇幻插画风格，梦幻色彩，魔法元素",
      realistic: "写实插画风格，细节丰富，自然色彩",
      anime: "日式动漫风格，大眼睛，夸张表情"
    };

    const styleDescription = styleDescriptions[style] || styleDescriptions.cartoon;

    // 提取base64数据
    const base64Data = characterAnchor.replace(/^data:image\/[a-z]+;base64,/, '');

    // 构建图像编辑指令
    const editInstruction = `
基于提供的角色图片，生成一幅新的儿童绘本插图。

**核心编辑指令**: ${editPrompt}

**保持一致性要求:**
1. **角色外观**: 严格保持角色的核心特征（发型、脸型、服装颜色、体型比例）与输入图片完全一致
2. **艺术风格**: 整个画面必须与输入图片的${styleDescription}风格保持完全一致
3. **色彩调色板**: 使用与基准图相同的色彩方案和饱和度
4. **线条风格**: 保持与基准图相同的线条粗细和绘画技法

**变化要求:**
- 只改变角色的姿势、表情和所处环境
- 根据编辑指令添加新的场景元素和背景
- 确保画面构图适合儿童绘本，温馨友好

**技术要求:**
- 高质量插图，细节丰富
- 适合儿童观看，无暴力内容
- 画面比例适合绘本页面
    `.trim();

    try {
      // 导入 Modality 枚举
      const { Modality } = await import('@google/genai');

      // 构建多模态内容
      const contents = [
        { text: editInstruction },
        {
          inlineData: {
            mimeType: "image/png",
            data: base64Data,
          },
        },
      ];

      // 设置超时控制
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('图像编辑超时 (40秒)')), 40000);
      });

      const generatePromise = this.ai.models.generateContent({
        model: 'gemini-2.0-flash-preview-image-generation',
        contents: contents,
        config: {
          responseModalities: [Modality.IMAGE],
          temperature: 0.6, // 稍低的温度保持一致性
          maxOutputTokens: 1024,
        },
      });

      const response = await Promise.race([generatePromise, timeoutPromise]);

      // 检查响应中是否包含图片数据
      if (response.candidates && response.candidates.length > 0) {
        const parts = response.candidates[0].content.parts;

        for (const part of parts) {
          if (part.inlineData && part.inlineData.data) {
            const imageData = part.inlineData.data;
            const imageDataUrl = `data:image/png;base64,${imageData}`;
            console.log(`✅ [图像编辑] 第 ${pageNumber} 张插图生成成功，数据大小: ${imageData.length} 字符`);
            return imageDataUrl;
          }
        }

        throw new Error('图像编辑响应中没有图片数据');
      } else {
        throw new Error('图像编辑API返回的响应无效');
      }

    } catch (error) {
      console.error(`❌ [图像编辑] 第 ${pageNumber} 张插图生成失败: ${this.formatError(error)}`);
      throw error;
    }
  }

  /**
   * 基于基准图生成占位符
   */
  private async generatePlaceholderFromAnchor(characterAnchor: string, text: string, style: StoryStyle): Promise<string> {
    console.log(`🔄 [占位符] 基于基准图生成占位符: ${text}`);
    
    try {
      // 尝试基于基准图生成一个简单的占位符场景
      const placeholderPrompt = `将角色置于一个简单的中性场景中，角色保持友善的表情，背景简洁。添加文字"${text}"作为标识。`;
      
      return await this.generateImageFromAnchor(characterAnchor, placeholderPrompt, style, 0);
    } catch (error) {
      console.warn(`⚠️ [占位符] 基于基准图生成失败，使用默认占位符`);
      // 降级到传统占位符
      return this.generatePlaceholderImage(800, 600, text);
    }
  }

  /**
   * 生成默认角色基准图
   */
  private generateDefaultCharacterAnchor(style: StoryStyle): string {
    console.log(`🎭 [默认基准图] 生成${style}风格的默认角色基准图`);
    
    // 创建一个简单的SVG角色基准图
    const svg = `
      <svg width="400" height="600" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#ffffff"/>
        
        <!-- 角色轮廓 -->
        <circle cx="200" cy="150" r="60" fill="#fdbcb4" stroke="#333" stroke-width="2"/>
        
        <!-- 眼睛 -->
        <circle cx="180" cy="140" r="8" fill="#333"/>
        <circle cx="220" cy="140" r="8" fill="#333"/>
        <circle cx="182" cy="138" r="3" fill="#fff"/>
        <circle cx="222" cy="138" r="3" fill="#fff"/>
        
        <!-- 嘴巴 -->
        <path d="M 185 165 Q 200 175 215 165" stroke="#333" stroke-width="2" fill="none"/>
        
        <!-- 身体 -->
        <rect x="160" y="210" width="80" height="120" rx="10" fill="#4a90e2"/>
        
        <!-- 手臂 -->
        <rect x="120" y="220" width="40" height="15" rx="7" fill="#fdbcb4"/>
        <rect x="240" y="220" width="40" height="15" rx="7" fill="#fdbcb4"/>
        
        <!-- 腿 -->
        <rect x="170" y="330" width="20" height="80" rx="10" fill="#2c5aa0"/>
        <rect x="210" y="330" width="20" height="80" rx="10" fill="#2c5aa0"/>
        
        <!-- 脚 -->
        <ellipse cx="180" cy="420" rx="15" ry="8" fill="#8b4513"/>
        <ellipse cx="220" cy="420" rx="15" ry="8" fill="#8b4513"/>
        
        <!-- 头发 -->
        <path d="M 140 120 Q 200 80 260 120 Q 240 100 200 100 Q 160 100 140 120" fill="#8b4513"/>
        
        <text x="200" y="500" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#666">
          默认角色基准图 (${style})
        </text>
      </svg>
    `;

    // 将SVG转换为base64
    const base64Svg = Buffer.from(svg).toString('base64');
    return `data:image/svg+xml;base64,${base64Svg}`;
  }

  /**
   * 传统图片生成模式（降级方案）
   */
  private async generateImagesTraditional(imagePrompts: string[], style: StoryStyle, subscription?: any): Promise<string[]> {
    console.log(`🔄 [传统模式] 降级到传统图片生成模式`);
    
    const stylePrompts = {
      cartoon: "卡通风格，色彩鲜艳，线条清晰，适合儿童，温馨友好",
      watercolor: "水彩画风格，柔和色调，艺术感强，梦幻效果",
      sketch: "简笔画风格，线条简洁，黑白或淡彩，手绘感",
      fantasy: "奇幻风格，梦幻色彩，魔法元素，想象力丰富",
      realistic: "写实风格，细节丰富，自然色彩，真实感强",
      anime: "动漫风格，大眼睛，夸张表情，日式风格"
    };

    const images: string[] = [];
    let successCount = 0;
    let failureCount = 0;

    for (let i = 0; i < imagePrompts.length; i++) {
      const prompt = imagePrompts[i];
      console.log(`🖼️ [传统模式] 生成第 ${i + 1}/${imagePrompts.length} 张图片`);

      try {
        const imageResult = await this.generateSingleImageWithRetry(prompt, style, stylePrompts[style], i + 1, 2, subscription);
        images.push(imageResult);
        successCount++;
        console.log(`✅ [传统模式] 第 ${i + 1} 张图片生成成功`);
      } catch (error) {
        failureCount++;
        console.error(`❌ [传统模式] 第 ${i + 1} 张图片生成失败:`, this.formatError(error));

        // 使用本地生成的占位符图片
        const placeholderBase64 = this.generatePlaceholderImage(800, 600, `第${i + 1}页`);
        images.push(placeholderBase64);
        console.log(`🔄 [传统模式] 使用占位符图片替代第 ${i + 1} 张图片`);
      }

      // 添加延迟避免请求过快
      if (i < imagePrompts.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    console.log(`🎯 [传统模式] 图片生成完成 - 成功: ${successCount}, 失败: ${failureCount}, 总计: ${images.length}`);
    return images;
  }

  /**
   * 生成占位符图片（base64格式）
   */
  private generatePlaceholderImage(width: number, height: number, text: string): string {
    // 创建一个简单的SVG占位符图片
    const svg = `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#f0f0f0"/>
        <rect x="10" y="10" width="${width - 20}" height="${height - 20}" fill="none" stroke="#ddd" stroke-width="2" stroke-dasharray="5,5"/>
        <text x="50%" y="50%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="24" fill="#999">
          ${text}
        </text>
        <text x="50%" y="60%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="16" fill="#ccc">
          图片生成失败
        </text>
      </svg>
    `;

    // 将SVG转换为base64
    const base64Svg = Buffer.from(svg).toString('base64');
    return `data:image/svg+xml;base64,${base64Svg}`;
  }

  /**
   * 生成占位符音频（base64格式）
   */
  private generatePlaceholderAudio(text: string): string {
    // 创建一个简单的静音音频文件（WAV格式）
    // 这是一个44字节的WAV文件头 + 1秒的静音数据
    const sampleRate = 44100;
    const duration = 1; // 1秒
    const numSamples = sampleRate * duration;
    const numChannels = 1;
    const bytesPerSample = 2;

    const buffer = new ArrayBuffer(44 + numSamples * bytesPerSample);
    const view = new DataView(buffer);

    // WAV文件头
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, 36 + numSamples * bytesPerSample, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, numChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * numChannels * bytesPerSample, true);
    view.setUint16(32, numChannels * bytesPerSample, true);
    view.setUint16(34, 8 * bytesPerSample, true);
    writeString(36, 'data');
    view.setUint32(40, numSamples * bytesPerSample, true);

    // 静音数据（全部为0）
    for (let i = 0; i < numSamples; i++) {
      view.setInt16(44 + i * bytesPerSample, 0, true);
    }

    // 转换为base64
    const uint8Array = new Uint8Array(buffer);
    const base64Audio = Buffer.from(uint8Array).toString('base64');
    return `data:audio/wav;base64,${base64Audio}`;
  }
}