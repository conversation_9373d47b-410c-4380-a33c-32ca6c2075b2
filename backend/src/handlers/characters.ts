import { Hono } from 'hono';
import { GeminiService } from '../services/gemini';
import { StorageService } from '../services/storage';
import type { Env } from '../types/hono';

const app = new Hono<{ Bindings: Env }>();

/**
 * 生成角色形象
 * POST /api/characters/generate
 */
app.post('/generate', async (c) => {
  try {
    const body = await c.req.json();
    const { characterName, characterAge, characterTraits, style, gender = 'neutral' } = body;

    // 验证输入参数
    if (!characterName || !characterAge || !characterTraits) {
      return c.json({
        success: false,
        error: '缺少必要参数'
      }, 400);
    }

    if (!Array.isArray(characterTraits) || characterTraits.length === 0) {
      return c.json({
        success: false,
        error: '角色特征不能为空'
      }, 400);
    }

    console.log(`🎭 [角色生成] 开始生成角色形象: ${characterName}, ${characterAge}岁, 性别: ${gender}, 风格: anime`);

    // 初始化服务
    const geminiService = new GeminiService(c.env.GEMINI_API_KEY);
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);

    // 生成角色形象（固定使用动漫风格）
    const characterImageUrl = await generateCharacterImage(
      geminiService,
      storageService,
      {
        characterName,
        characterAge,
        characterTraits,
        style: 'anime', // 固定使用动漫风格
        gender
      }
    );

    console.log(`✅ [角色生成] 角色形象生成成功: ${characterImageUrl}`);

    return c.json({
      success: true,
      data: {
        imageUrl: characterImageUrl,
        characterName,
        characterAge,
        characterTraits,
        style: 'anime', // 固定返回动漫风格
        gender
      }
    });

  } catch (error) {
    console.error('❌ [角色生成] 角色形象生成失败:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : '角色形象生成失败'
    }, 500);
  }
});

/**
 * 角色形象生成核心逻辑
 */
async function generateCharacterImage(
  geminiService: GeminiService,
  storageService: StorageService,
  params: {
    characterName: string;
    characterAge: number;
    characterTraits: string[];
    style: string;
    gender: string;
  }
): Promise<string> {
  const { characterName, characterAge, characterTraits, style, gender } = params;

  // 构建角色描述
  const traitsDescription = characterTraits.join('、');

  // 性别描述映射
  const genderDescriptions = {
    male: '男孩',
    female: '女孩',
    neutral: '儿童'
  };

  const genderDescription = genderDescriptions[gender as keyof typeof genderDescriptions] || genderDescriptions.neutral;

  // 固定使用动漫风格
  const styleDescription = "日式动漫风格，大眼睛，夸张表情，色彩饱和度高，可爱清新";

  // 将style转换为StoryStyle类型
  const storyStyle = style as 'cartoon' | 'watercolor' | 'sketch' | 'fantasy' | 'realistic' | 'anime';

  // 构建角色生成提示词
  const characterPrompt = `
你是一位专业的角色设计师，为儿童绘本创作主角形象。

**角色信息:**
- 姓名: ${characterName}
- 年龄: ${characterAge}岁
- 性别: ${genderDescription}
- 性格特征: ${traitsDescription}

**生成要求:**
1. **角色设计**: 创作一个${characterAge}岁的可爱${genderDescription}角色，体现${traitsDescription}的性格特征
2. **外观特点**:
   - ${gender === 'male' ? '男孩特征，短发，活泼阳光的形象' :
      gender === 'female' ? '女孩特征，可爱发型，温柔甜美的形象' :
      '中性特征，适合儿童的友好形象'}
   - 友善的面部表情，大眼睛，温暖的笑容
   - 适合年龄的服装，简洁但有特色
   - 整体形象要可爱、亲切、容易让儿童喜爱
3. **构图要求**:
   - 全身像，角色居中
   - 正面朝向，站立姿势
   - 纯白色背景，无其他元素
   - 高分辨率，细节清晰
4. **艺术风格**: ${styleDescription}

**重要说明:**
- 这个角色将在整个故事中保持一致的外观
- 请确保角色形象适合儿童观看，积极正面
- 角色应该具有很强的辨识度和记忆点

请生成一张高质量的角色设定图。
  `.trim();

  try {
    // 使用Gemini生成角色形象
    console.log(`🎨 [角色生成] 开始调用Gemini生成角色形象`);

    const imageDataUrl = await geminiService.generateSingleImage(characterPrompt, storyStyle);
    
    if (!imageDataUrl) {
      throw new Error('Gemini返回的图像数据为空');
    }

    console.log(`🖼️ [角色生成] Gemini生成完成，开始上传到存储`);

    // 生成唯一的文件名
    const timestamp = Date.now();
    const fileName = `characters/${characterName}-${timestamp}.jpg`;

    // 上传到存储服务
    const uploadedUrl = await storageService.uploadImage(fileName, imageDataUrl);

    if (!uploadedUrl) {
      throw new Error('角色形象上传失败');
    }

    console.log(`✅ [角色生成] 角色形象上传成功: ${uploadedUrl}`);
    return uploadedUrl;

  } catch (error) {
    console.error(`❌ [角色生成] 生成失败:`, error);
    
    // 生成占位符角色形象
    console.log(`🔄 [角色生成] 使用占位符角色形象`);
    return generatePlaceholderCharacter(characterName, characterAge, storyStyle);
  }
}

/**
 * 生成占位符角色形象
 */
function generatePlaceholderCharacter(
  characterName: string, 
  characterAge: number, 
  style: string
): string {
  // 根据风格选择颜色
  const styleColors = {
    cartoon: '#FF6B6B',
    watercolor: '#4ECDC4',
    sketch: '#95A5A6',
    fantasy: '#9B59B6',
    realistic: '#3498DB',
    anime: '#E74C3C'
  };

  const color = styleColors[style as keyof typeof styleColors] || styleColors.cartoon;

  // 生成SVG占位符
  const svgContent = `
    <svg width="400" height="500" xmlns="http://www.w3.org/2000/svg">
      <rect width="400" height="500" fill="#f8f9fa"/>
      <circle cx="200" cy="150" r="60" fill="${color}" opacity="0.3"/>
      <rect x="150" y="220" width="100" height="120" fill="${color}" opacity="0.2" rx="10"/>
      <text x="200" y="380" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" fill="${color}">
        ${characterName}
      </text>
      <text x="200" y="410" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#666">
        ${characterAge}岁
      </text>
      <text x="200" y="440" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#999">
        ${style}风格
      </text>
    </svg>
  `;

  // 转换为base64
  const base64Svg = btoa(unescape(encodeURIComponent(svgContent)));
  return `data:image/svg+xml;base64,${base64Svg}`;
}

export default app;
